{"rustc": 15497389221046826682, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-targets\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-targets\"]", "target": 15315924755136109342, "profile": 8276155916380437441, "path": 12687914124843399096, "deps": [[411067296443658118, "serde", false, 17890964873241453217], [5157631553186200874, "num_traits", false, 4324963778297291552], [7910860254152155345, "iana_time_zone", false, 2436790285605642721]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/chrono-53c43d537fc1107b/dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}