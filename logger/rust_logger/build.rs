use std::env;
use std::path::Path;

fn main() {
    // 确保生成目录存在
    std::fs::create_dir_all("src/generated").expect("Failed to create generated directory");

    // 检查是否已经有生成的文件，如果有则跳过编译
    let generated_files = ["src/generated/log_metadata.rs", "src/generated/logger_generated.rs"];
    let has_generated_files = generated_files.iter().any(|f| Path::new(f).exists());

    if has_generated_files {
        println!("cargo:warning=Using existing generated protobuf files");
    } else {
        // 编译protobuf文件
        let mut config = prost_build::Config::new();
        config.out_dir("src/generated");

        // 尝试编译protobuf文件
        match config.compile_protos(&["proto/log_metadata.proto"], &["proto/"]) {
            Ok(_) => {
                println!("cargo:warning=Successfully compiled protobuf files");
            }
            Err(e) => {
                eprintln!("Warning: Failed to compile protobuf files: {}", e);
                eprintln!("This might be due to missing protoc in CI environment.");
                eprintln!("Make sure to commit the generated files to avoid this issue.");

                // 在CI环境中，如果protoc不可用，我们应该使用预生成的文件
                // 这里不panic，而是给出警告
                println!("cargo:warning=Protobuf compilation failed, please ensure generated files are committed");
            }
        }
    }

    // 删除多余的_.rs文件（如果存在）
    let underscore_file = "src/generated/_.rs";
    if std::path::Path::new(underscore_file).exists() {
        std::fs::remove_file(underscore_file).ok();
    }

    // 根据目标平台配置（移除xlog链接，使用纯Rust实现）
    let target = env::var("TARGET").unwrap();
    match target.as_str() {
        "aarch64-linux-android" => {
            println!("cargo:rustc-link-arg=-Wl,-z,max-page-size=16384");
        }
        "aarch64-unknown-linux-ohos" => {
            println!("cargo:rustc-link-arg=-Wl,-z,max-page-size=16384");
            napi_build_ohos::setup();
        }
        _ => {
            // 纯Rust实现，无需平台特定链接
            println!("cargo:warning=Pure Rust logger implementation for target: {}", target);
        }
    }
}


