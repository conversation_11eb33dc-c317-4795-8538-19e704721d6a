// https://developer.huawei.com/consumer/cn/doc/harmonyos-guides-V5/typescript-to-arkts-migration-guide-0000001820879565-V5?catalogVersion=V5
import { RustChannel } from '@uplus/rust_ffi';
import { LoggerResult } from './LoggerResult';
import { LoggerConfig, LogLevel } from './LoggerConfig';
import { LargeFileCallback } from './LargeFileCallback';
import { LoggerConstant, LoggerCode, LoggerErrorMessage } from './LoggerConstant';
import { ByteBuffer } from '@ohos/flatbuffers';
import {
  LoggerFlat,
  BoolWrapper,
  StrWrapper,
  Int32Wrapper,
  LoggerContainer,
  LoggerMessage
} from '../generated/com/haier/uhome/uplus/rust/logger/fbs';

/**
 * 日志条目接口（遵循Android逻辑）
 * 时间戳由Rust层内部生成，确保准确性和一致性
 */
interface LogEntry {
  level: LogLevel;
  tag: string;
  format: string;
  args: string[];
}



const TAG = "RustLogger";

export class RustLogger {
  // 性能优化：预编译正则表达式，避免每次调用时重新编译
  // 鸿蒙只支持s(字符串)、d(十进制)、i(整数)三种格式说明符
  private static readonly HARMONY_FORMAT_REGEX = /%\{(?:public|private)\}[sdi]/g;

  // 大文件回调
  private static largeFileCallback: LargeFileCallback | null = null;
  
  /**
   * 初始化日志器（异步执行，不返回结果）
   */
  static initialize(context: Context, config: LoggerConfig): void {
    // 设置默认日志目录
    if (!config.logDirectory) {
      config.logDirectory = `${context.filesDir}/logs`;
    }
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "initialize");
    params.set("config", JSON.stringify(config));
    // 异步调用，不等待返回结果
    RustChannel.getInstance().getRustBufferAsync(LoggerConstant.LibName, params, () => {});
  }

  /**
   * 写入DEBUG级别日志
   */
  static debug(tag: string, message: string, ...args: Object[]): void {
    RustLogger.writeLog(LogLevel.DEBUG, tag, message, args);
  }

  /**
   * 写入INFO级别日志
   */
  static info(tag: string, message: string, ...args: Object[]): void {
    RustLogger.writeLog(LogLevel.INFO, tag, message, args);
  }

  /**
   * 写入WARN级别日志
   */
  static warn(tag: string, message: string, ...args: Object[]): void {
    RustLogger.writeLog(LogLevel.WARN, tag, message, args);
  }

  /**
   * 写入ERROR级别日志
   */
  static error(tag: string, message: string, ...args: Object[]): void {
    RustLogger.writeLog(LogLevel.ERROR, tag, message, args);
  }

  /**
   * 写入日志（内部方法，异步执行，高性能）
   */
  private static writeLog(level: LogLevel, tag: string, message: string, args: Object[] = []): void {
    // 检查是否为鸿蒙格式（包含 %{public} 或 %{private}）
    const isHarmonyFormat = RustLogger.isHarmonyFormat(message);

    let finalFormat = message;

    if (isHarmonyFormat) {
      // 转换鸿蒙格式为Rust格式（只做格式转换，脱敏交给Rust层处理）
      finalFormat = RustLogger.convertHarmonyToRustFormat(message);
    }

    // 转换参数为字符串数组（在桥接层统一处理）
    const stringArgs: string[] = args.map(arg => String(arg));

    // 统一使用新的logger逻辑（遵循Android逻辑，不传递timestamp）
    const logEntry: LogEntry = {
      level: level,
      tag: tag,
      format: finalFormat,
      args: stringArgs
    };

    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "write_log");
    params.set("log_entry", JSON.stringify(logEntry));

    // 异步调用，不等待返回结果，提高性能
    RustChannel.getInstance().getRustBufferAsync(LoggerConstant.LibName, params, () => {
      // 日志写入通常不需要处理回调
    });
  }

  /**
   * 更新用户ID（异步执行）
   */
  static updateUserId(userId: string): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "update_user_id");
    params.set("user_id", userId);

    // 异步调用，不等待返回结果
    RustChannel.getInstance().getRustBufferAsync(LoggerConstant.LibName, params, () => {
      // 用户ID更新完成，无需额外处理
    });
  }

  /**
   * 写入崩溃日志（异步执行）
   */
  static writeCrashLog(crashInfo: string): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "write_crash_log");
    params.set("crash_info", crashInfo);

    // 异步调用，不等待返回结果
    RustChannel.getInstance().getRustBufferAsync(LoggerConstant.LibName, params, () => {
      // 崩溃日志写入完成，无需额外处理
    });
  }

  // isInitialized 接口已删除 - 不需要此接口

  /**
   * 设置大文件通知回调
   * 当日志文件超过设置的大小限制时会触发此回调
   */
  static setLargeFileCallback(callback: LargeFileCallback | null): string {
    RustLogger.largeFileCallback = callback;

    if (callback) {
      // 注册监听器，参考userdomain的实现
      const params = new Map<string, string>();
      params.set(LoggerConstant.Action, "add_large_file_observer");
      params.set(LoggerConstant.ListenerId, "0");

      const buffer = RustChannel.getInstance().manageRustBufferListenerWithData(
        LoggerConstant.LibName,
        params,
        (eventBuffer) => {
          // 处理大文件通知事件
          RustLogger.handleLargeFileEvent(eventBuffer);
        }
      );

      const result = RustLogger.parseLoggerResult<string>(buffer);
      

      return result.data || '';
    }

    return '';
  }

  /**
   * 压缩日志文件供上传（异步执行，通过回调返回结果）
   * @param days 压缩最近几天的日志
   * @param callback 压缩完成回调，参数为压缩文件路径或错误信息
   */
  static compressLogsForUpload(days: number = 7, callback: (result: LoggerResult<string>) => void): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "compress_logs_for_upload");
    params.set("days", days.toString());

    // 使用回调版本的RustChannel调用
    RustChannel.getInstance().manageRustBufferListenerWithData(
      LoggerConstant.LibName,
      params,
      (buffer) => {
        const result = RustLogger.parseLoggerResult<string>(buffer);
        callback(result);
      }
    );
  }

  /**
   * 压缩崩溃日志供上传（异步执行，通过回调返回结果）
   * @param callback 压缩完成回调，参数为压缩文件路径或错误信息
   */
  static compressCrashLogs(callback: (result: LoggerResult<string>) => void): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "compress_crash_logs");

    // 使用回调版本的RustChannel调用
    RustChannel.getInstance().manageRustBufferListenerWithData(
      LoggerConstant.LibName,
      params,
      (buffer) => {
        const result = RustLogger.parseLoggerResult<string>(buffer);
        callback(result);
      }
    );
  }

  /**
   * 上传成功后清理文件（异步执行）
   * @param zipPath 压缩文件路径
   */
  static cleanupAfterUploadSuccess(zipPath: string): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "cleanup_after_upload_success");
    params.set("zip_path", zipPath);

    // 异步调用，不等待返回结果
    RustChannel.getInstance().getRustBufferAsync(LoggerConstant.LibName, params, () => {
      // 清理完成，无需额外处理
    });
  }

  /**
   * 上传失败后清理文件（异步执行）
   * @param zipPath 压缩文件路径
   */
  static cleanupAfterUploadFailure(zipPath: string): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "cleanup_after_upload_failure");
    params.set("zip_path", zipPath);

    // 异步调用，不等待返回结果
    RustChannel.getInstance().getRustBufferAsync(LoggerConstant.LibName, params, () => {
      // 清理完成，无需额外处理
    });
  }

  /**
   * 启用控制台日志输出（异步执行）
   * 对应Android的enableConsoleLog()
   */
  static enableConsoleLog(enable: boolean): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "enable_console_log");
    params.set("enable", enable.toString());

    // 异步调用，不等待返回结果
    RustChannel.getInstance().getRustBufferAsync(LoggerConstant.LibName, params, () => {
      // 控制台日志设置完成，无需额外处理
    });
  }

  /**
   * 启用完整日志模式（异步执行）
   * 对应Android的enableFullLogs()
   * 当启用时，设置日志级别为DEBUG并输出所有日志到文件
   */
  static enableFullLogs(enable: boolean): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "enable_full_logs");
    params.set("enable", enable.toString());

    // 异步调用，不等待返回结果
    RustChannel.getInstance().getRustBufferAsync(LoggerConstant.LibName, params, () => {
      // 完整日志模式设置完成，无需额外处理
    });
  }

  /**
   * 获取控制台日志输出状态（异步执行）
   * 对应Android的isEnableConsole()
   */
  static isConsoleLogEnabled(callback: (result: LoggerResult<boolean>) => void): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "is_console_log_enabled");

    // 使用回调版本的RustChannel调用
    RustChannel.getInstance().manageRustBufferListenerWithData(
      LoggerConstant.LibName,
      params,
      (buffer) => {
        const result = RustLogger.parseLoggerResult<boolean>(buffer);
        callback(result);
      }
    );
  }

  /**
   * 获取完整日志模式状态（异步执行）
   * 对应Android的getFullLogsStatus()
   */
  static isFullLogsEnabled(callback: (result: LoggerResult<boolean>) => void): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "is_full_logs_enabled");

    // 使用回调版本的RustChannel调用
    RustChannel.getInstance().manageRustBufferListenerWithData(
      LoggerConstant.LibName,
      params,
      (buffer) => {
        const result = RustLogger.parseLoggerResult<boolean>(buffer);
        callback(result);
      }
    );
  }

  /**
   * 获取当前日志级别（异步执行）
   * 对应Android的getLoggerLevel()
   */
  static getLogLevel(callback: (result: LoggerResult<LogLevel>) => void): void {
    const params = new Map<string, string>();
    params.set(LoggerConstant.Action, "get_log_level");

    // 使用回调版本的RustChannel调用
    RustChannel.getInstance().manageRustBufferListenerWithData(
      LoggerConstant.LibName,
      params,
      (buffer) => {
        const result = RustLogger.parseLoggerResult<number>(buffer);
        // 转换数字为LogLevel枚举
        const logLevel = RustLogger.convertIntToLogLevel(result.data || 0);
        const convertedResult = new LoggerResult<LogLevel>(
          result.success,
          result.code,
          result.message,
          logLevel
        );
        callback(convertedResult);
      }
    );
  }

  /**
   * 处理大文件事件
   * 由RustChannel监听器调用
   */
  private static handleLargeFileEvent(eventBuffer: ArrayBuffer): void {
    try {
      // 解析事件数据，使用FlatBuffer格式
      const eventFlat: LoggerMessage = LoggerMessage.getRootAsLoggerMessage(new ByteBuffer(new Uint8Array(eventBuffer)));
      const code: number = eventFlat.code();

      // 大文件事件接收，code: ${code}

      // 检查是否是大文件事件（code为1001表示大文件事件）
      if (code === 1001) {
        // 解析容器中的大文件事件数据
        const containerType: LoggerContainer = eventFlat.containerType();
        if (containerType === LoggerContainer.LargeFileEvent) {
          // 这里需要根据实际的FlatBuffer结构来解析文件路径
          // 暂时使用简单的逻辑，后续可以完善
          if (RustLogger.largeFileCallback) {
            RustLogger.largeFileCallback.onLargeFileDetected("large_file_detected");
          }
        }
      }
    } catch (error) {
      console.error(`${TAG}: Failed to handle large file event:`, error);
    }
  }

  /**
   * 解析LoggerResult（FlatBuffer格式）
   */
  private static parseLoggerResult<T>(buffer: ArrayBuffer | null): LoggerResult<T> {
    if (!buffer) {
      return new LoggerResult<T>(false, LoggerCode.RustCallFailure, LoggerErrorMessage.RustCallFailure, undefined);
    }

    try {
      const flat: LoggerFlat = LoggerFlat.getRootAsLoggerFlat(new ByteBuffer(new Uint8Array(buffer)));
      const success: boolean = flat.success();
      const code: number = flat.code();
      const message: string = flat.message() || '';

      // 根据容器类型解析数据
      let data: T | undefined = undefined;
      const containerType: LoggerContainer = flat.containerType();

      if (success && containerType) {
        switch (containerType) {
          case LoggerContainer.BoolWrapper:
            const boolWrapper: BoolWrapper = flat.container(new BoolWrapper()) as BoolWrapper;
            data = boolWrapper.value() as T;
            break;
          case LoggerContainer.StrWrapper:
            const strWrapper: StrWrapper = flat.container(new StrWrapper()) as StrWrapper;
            data = strWrapper.value() as T;
            break;
          case LoggerContainer.Int32Wrapper:
            const intWrapper: Int32Wrapper = flat.container(new Int32Wrapper()) as Int32Wrapper;
            data = intWrapper.value() as T;
            break;
          // 其他类型可以根据需要添加
        }
      }

      return new LoggerResult<T>(success, code, message, data);
    } catch (error) {
      console.error(`${TAG}: Failed to parse logger result:`, error);
      return new LoggerResult<T>(false, LoggerCode.RustCallFailure, "Failed to parse FlatBuffer result", undefined);
    }
  }

  /**
   * 判断是否为鸿蒙格式
   * 性能优化：使用indexOf代替includes，避免正则表达式
   */
  private static isHarmonyFormat(message: string): boolean {
    return message.indexOf('%{public}') !== -1 || message.indexOf('%{private}') !== -1;
  }

  /**
   * 转换鸿蒙格式为Rust格式
   * 支持鸿蒙格式说明符：s(字符串), d(十进制), i(整数)
   */
  private static convertHarmonyToRustFormat(message: string): string {
    // 性能优化：使用预编译的正则表达式
    // 重置正则表达式的lastIndex，确保每次都从头开始匹配
    RustLogger.HARMONY_FORMAT_REGEX.lastIndex = 0;
    return message.replace(RustLogger.HARMONY_FORMAT_REGEX, '{}');
  }

  /**
   * 转换整数为LogLevel枚举
   * Rust层映射: Debug=0, Info=1, Warn=2, Error=3
   * HarmonyOS层映射: DEBUG=0, INFO=1, WARN=2, ERROR=3
   */
  private static convertIntToLogLevel(value: number): LogLevel {
    switch (value) {
      case 0: return LogLevel.DEBUG;  // Rust Debug -> HarmonyOS DEBUG
      case 1: return LogLevel.INFO;   // Rust Info -> HarmonyOS INFO
      case 2: return LogLevel.WARN;   // Rust Warn -> HarmonyOS WARN
      case 3: return LogLevel.ERROR;  // Rust Error -> HarmonyOS ERROR
      default: return LogLevel.WARN;  // 默认值
    }
  }

}
