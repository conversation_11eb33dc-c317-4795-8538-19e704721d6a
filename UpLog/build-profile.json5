{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_UpLog_FDkTT-1ubaKcgL1psW2NtoHcuxddU-T27rxNje2XF3U=.cer",
          "storePassword": "0000001B13FDEFD61648C3AC4A1481C348EABE9F3F39779139AAE4C8473B2F7BBE29744466554A84DE7399",
          "keyAlias": "debugKey",
          "keyPassword": "0000001B167B0C3C4587A14073EA1E41244CFB27CAA7A5D6299A9A1B7ECF22DC7337681AF0548449BD5828",
          "profile": "/Users/<USER>/.ohos/config/default_UpLog_FDkTT-1ubaKcgL1psW2NtoHcuxddU-T27rxNje2XF3U=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_UpLog_FDkTT-1ubaKcgL1psW2NtoHcuxddU-T27rxNje2XF3U=.p12"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.2(14)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "useNormalizedOHMUrl": true
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "uplog",
      "srcPath": "./uplog",
    }
  ]
}