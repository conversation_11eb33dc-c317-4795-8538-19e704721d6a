import { LogLevel, UpLoggerInjection } from '@uplus/uplog/Index';
import { logger } from '../EntryLog';
import { promptAction } from '@kit.ArkUI';

@Entry
@Component
struct Index {
  @State message: string = 'Rust Logger 功能测试';
  @State logOutput: string = '';
  @State currentLogLevel: string = 'WARN';
  @State consoleEnabled: boolean = false;
  @State fullLogsEnabled: boolean = false;
  @State uploadProgress: number = 0;
  @State isUploading: boolean = false;

  private scroller: Scroller = new Scroller();

  onPageShow(): void {
    this.updateStatus();
  }

  addLog(message: string) {
    const timestamp = new Date().toLocaleTimeString();
    this.logOutput += `[${timestamp}] ${message}\n`;
    // 自动滚动到底部
    setTimeout(() => {
      this.scroller.scrollTo({ xOffset: 0, yOffset: this.scroller.currentOffset().yOffset + 1000 });
    }, 100);
  }

  clearLogs() {
    this.logOutput = '';
  }

  async updateStatus(): Promise<void> {
    // 获取当前状态（异步）
    try {
      const level: LogLevel = await UpLoggerInjection.getLoggerLevel();
      this.currentLogLevel = LogLevel[level];

      this.consoleEnabled = await UpLoggerInjection.isEnableConsole();
      this.fullLogsEnabled = await UpLoggerInjection.getFullLogsStatus();
    } catch (error) {
      this.addLog(`状态更新失败: ${error}`);
    }
  }

  async showToast(message: string) {
    promptAction.showToast({
      message: message,
      duration: 2000
    });
  }

  build() {
    Column() {
      // 标题
      Text(this.message)
        .id('HelloWorld')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 20, bottom: 20 })

      // 状态指示器
      Column() {
        Row() {
          Text('日志级别: ')
            .fontSize(16)
          Text(this.currentLogLevel)
            .fontSize(16)
            .fontColor(Color.Blue)

          Blank()

          Text(`控制台: ${this.consoleEnabled ? '开启' : '关闭'}`)
            .fontSize(14)
            .fontColor(this.consoleEnabled ? Color.Green : Color.Red)
        }
        .width('100%')
        .margin({ bottom: 5 })

        Row() {
          Text(`完整日志: ${this.fullLogsEnabled ? '开启' : '关闭'}`)
            .fontSize(14)
            .fontColor(this.fullLogsEnabled ? Color.Green : Color.Red)
        }
        .width('100%')
        .justifyContent(FlexAlign.Start)
      }
      .width('90%')
      .padding(10)
      .backgroundColor('#f8f9fa')
      .borderRadius(8)
      .margin({ bottom: 20 })

      // 基础日志测试
      Text('📝 基础日志测试')
        .fontSize(18)
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: 10 })

      Flex({ wrap: FlexWrap.Wrap, justifyContent: FlexAlign.SpaceEvenly }) {
        Button('Debug日志')
          .width('45%')
          .margin(5)
          .onClick(() => {
            logger().debug("Debug测试: %{public}s", "debug_data");
            logger().debug("Debug格式化: %{public}s %{public}d", "test", 123);
            this.addLog('📤 发送Debug日志');
          })

        Button('Info日志')
          .width('45%')
          .margin(5)
          .onClick(() => {
            logger().info("Info测试: %{public}s", "info_data");
            logger().info("Info时间戳: %{public}s", new Date().toISOString());
            this.addLog('📤 发送Info日志');
          })

        Button('Warn日志')
          .width('45%')
          .margin(5)
          .onClick(() => {
            logger().warn("Warn测试: %{public}s", "warn_data");
            logger().warn("Warn操作失败: %{public}s code %{public}d", "network", 404);
            this.addLog('📤 发送Warn日志');
          })

        Button('Error日志')
          .width('45%')
          .margin(5)
          .onClick(() => {
            logger().error("Error测试: %{public}s", "error_data");
            logger().error("Error异常: %{public}s %{public}s", "database", "connection_timeout");
            this.addLog('📤 发送Error日志');
          })
      }
      .margin({ bottom: 20 })

      // 高级功能测试
      Text('⚙️ 高级功能测试')
        .fontSize(18)
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: 10 })

      Flex({ wrap: FlexWrap.Wrap, justifyContent: FlexAlign.SpaceEvenly }) {
        Button('设置级别')
          .width('30%')
          .margin(5)
          .onClick(async () => {
            UpLoggerInjection.provideManager().setLogLevel(LogLevel.ERROR);
            // 等待一下再更新状态
            setTimeout(async () => {
              await this.updateStatus();
            }, 500);
            this.addLog('🔧 设置日志级别为ERROR');
          })

        Button('敏感数据')
          .width('30%')
          .margin(5)
          .onClick(() => {
            logger().warn("敏感数据测试: 用户名=%{private}s, 密码=%{private}s, 公开信息=%{public}s",
              "user123", "password123", "login_success");
            logger().error("敏感错误: %{private}s", "database_connection_string");
            this.addLog('🔒 测试敏感数据脱敏');
          })

        Button('批量日志')
          .width('30%')
          .margin(5)
          .onClick(() => {
            for (let i = 0; i < 50; i++) {
              logger().info("批量日志测试 %{public}d: %{public}s", i, `test_data_${i}`);
            }
            this.addLog('📊 发送批量日志(50条)');
          })

        Button('更新用户')
          .width('30%')
          .margin(5)
          .onClick(() => {
            const newUserId = `user_${Date.now()}`;
            UpLoggerInjection.updateUserId(newUserId);
            logger().info("用户ID已更新为: %{public}s", newUserId);
            this.addLog(`👤 更新用户ID: ${newUserId}`);
          })

        Button('隐私协议')
          .width('30%')
          .margin(5)
          .onClick(() => {
            UpLoggerInjection.setPrivacyAgreed(true);
            logger().info("隐私协议状态已设置");
            this.addLog('📋 设置隐私协议状态');
          })

        Button('崩溃日志')
          .width('30%')
          .margin(5)
          .onClick(async () => {
            const crashInfo: string = `模拟崩溃信息:\nError: Test crash\nStack trace: at test() line 123\nTimestamp: ${new Date().toISOString()}`;
            const result: boolean = await UpLoggerInjection.writeCrashLog(crashInfo);
            this.addLog(`💥 写入崩溃日志: ${result ? '成功' : '失败'}`);
          })

        Button('控制台开关')
          .width('30%')
          .margin(5)
          .onClick(async () => {
            const newState = !this.consoleEnabled;
            UpLoggerInjection.enableConsoleLog(newState);
            // 等待一下再更新状态
            setTimeout(async () => {
              await this.updateStatus();
            }, 500);
            this.addLog(`🖥️ 控制台日志: ${newState ? '开启' : '关闭'}`);
          })
      }
      .margin({ bottom: 20 })

      // 文件操作测试
      Text('📁 文件操作测试')
        .fontSize(18)
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: 10 })

      Flex({ wrap: FlexWrap.Wrap, justifyContent: FlexAlign.SpaceEvenly }) {
        Button('上传日志')
          .width('45%')
          .margin(5)
          .enabled(!this.isUploading)
          .onClick(() => {
            this.isUploading = true;
            this.uploadProgress = 0;
            this.addLog('📤 开始上传日志...');

            UpLoggerInjection.uploadLogsManually((progress: number, message: string) => {
              this.uploadProgress = progress;
              this.addLog(`上传进度: ${progress}% - ${message}`);

              if (progress >= 100) {
                this.isUploading = false;
                this.addLog('✅ 日志上传完成');
              }
            });
          })

        Button('完整日志')
          .width('45%')
          .margin(5)
          .onClick(async () => {
            const newState = !this.fullLogsEnabled;
            UpLoggerInjection.enableFullLogs(newState);
            // 等待一下再更新状态
            setTimeout(async () => {
              await this.updateStatus();
            }, 500);
            this.addLog(`📋 完整日志模式: ${newState ? '开启' : '关闭'}`);
          })
      }
      .margin({ bottom: 20 })

      // 上传进度条
      if (this.isUploading) {
        Column() {
          Text(`上传进度: ${this.uploadProgress}%`)
            .fontSize(14)
            .margin({ bottom: 5 })

          Progress({ value: this.uploadProgress, total: 100, type: ProgressType.Linear })
            .width('90%')
            .height(20)
        }
        .margin({ bottom: 20 })
      }

      // 日志输出区域
      Row() {
        Text('📄 日志输出')
          .fontSize(16)
          .fontWeight(FontWeight.Medium)

        Blank()

        Button('清空')
          .fontSize(12)
          .height(30)
          .onClick(() => {
            this.clearLogs();
          })

        Button('刷新状态')
          .fontSize(12)
          .height(30)
          .margin({ left: 5 })
          .onClick(() => {
            this.updateStatus();
            this.addLog('🔄 状态已刷新');
          })
      }
      .width('90%')
      .margin({ bottom: 10 })

      Scroll(this.scroller) {
        Text(this.logOutput || '暂无日志输出...\n点击上方按钮开始测试Logger功能')
          .fontSize(12)
          .fontFamily('monospace')
          .textAlign(TextAlign.Start)
          .width('100%')
          .padding(10)
      }
      .width('90%')
      .height(200)
      .backgroundColor('#f5f5f5')
      .border({ width: 1, color: '#ddd', radius: 5 })
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Auto)

    }
    .height('100%')
    .width('100%')
    .padding(20)
  }
}