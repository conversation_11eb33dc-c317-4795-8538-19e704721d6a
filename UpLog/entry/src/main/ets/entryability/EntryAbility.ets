import { AbilityConstant, UIAbility, Want } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { FileLogger, UpLoggerInjection, UpLogConfig, LogLevel } from '@uplus/uplog/Index';

export default class EntryAbility extends UIAbility {
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onCreate');

    // 创建配置
    const config = UpLogConfig.builder()
      .setLogLevel(LogLevel.DEBUG)
      .setPrivacyAgreed(true) // 初始同意隐私协议
      .setFullLog(false) // 暂不开启全量日志
      .setFileConfig(
        20 * 1024 * 1024,  // 20MB单文件
        200 * 1024 * 1024  // 200MB目录总大小
      )
      .setUploadUrl("https://api.example.com/logs") // 设置上传地址
      .build();

    // 使用配置初始化
    UpLoggerInjection.initializeWithConfig(this.context, config);

    // 测试文件日志
    const logger = new FileLogger("uplog", true);
    logger.logToFile("Test", "onCreate with new config system");

    hilog.info(0x0000, 'testTag', 'UpLog initialized with config: %{public}s',
      JSON.stringify(config.toJSON()));
  }

  onDestroy(): void {
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onDestroy');
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageCreate');

    windowStage.loadContent('pages/Index', (err) => {
      if (err.code) {
        hilog.error(0x0000, 'testTag', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'testTag', 'Succeeded in loading the content.');
    });
  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground(): void {
    // Ability has brought to foreground
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onForeground');
  }

  onBackground(): void {
    // Ability has back to background
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onBackground');
  }
}
