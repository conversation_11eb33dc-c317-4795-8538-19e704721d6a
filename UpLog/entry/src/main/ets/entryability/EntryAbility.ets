import { AbilityConstant, UIAbility, Want } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { LogLevel, UpLogConfig, UpLoggerInjection } from 'uplog';

export default class EntryAbility extends UIAbility {
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onCreate');
    // 创建配置
    const config: UpLogConfig = UpLogConfig.builder()
      .setLogLevel(LogLevel.DEBUG)
      .setConsoleOutput(true) // 开启控制台输出用于调试
      .setPrivacyAgreed(true) // 初始同意隐私协议
      .setFullLog(false) // 暂不开启全量日志
      .setMaxFileSize(20 * 1024 * 1024) // 20MB单文件
      .setMaxDirectorySize(200 * 1024 * 1024) // 200MB目录总大小
      .build();

    // 使用配置初始化
    UpLoggerInjection.initializeWithConfig(this.context, config);

    hilog.info(0x0000, 'testTag', 'UpLog initialized with config successfully');
  }

  onDestroy(): void {
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onDestroy');
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageCreate');

    windowStage.loadContent('pages/Index', (err: Error) => {
      if (err) {
        hilog.error(0x0000, 'testTag', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'testTag', 'Succeeded in loading the content.');
    });
  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground(): void {
    // Ability has brought to foreground
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onForeground');
  }

  onBackground(): void {
    // Ability has back to background
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onBackground');
  }
}
