import { LogLevel } from '../log/LogLevel';
import { LoggerConfig, LogLevel as RustLogLevel } from '@uplus/rust_logger';

/**
 * JSON配置对象接口
 */
interface UpLogConfigJSON {
  logLevel: LogLevel;
  enableConsoleOutput: boolean;
  enableFileOutput: boolean;
  enableFullLog: boolean;
  logEnv: LogEnv;
  disableSensitiveWords: boolean;
  userId: string;
  appVersion: string;
  privacyAgreed: boolean;
  maxFileSize: number;
  maxDirectorySize: number;
  logDirectory: string;
  uploadUrl: string;
}

/**
 * 日志环境定义
 */
export enum LogEnv {
  /** 验收环境 */
  YS = "YS",
  /** 生产环境 */
  SC = "SC"
}

/**
 * UpLog 配置类
 * 简洁的配置类，用于传递给N-API日志库
 */
export class UpLogConfig {
  // 基础配置
  public readonly logLevel: LogLevel;
  public readonly enableConsoleOutput: boolean;
  public readonly enableFileOutput: boolean;
  public readonly enableFullLog: boolean; // 是否开启全量日志（触发上传）
  public readonly logEnv: LogEnv; // 日志环境
  public readonly disableSensitiveWords: boolean; // 是否禁用日志脱敏

  // 用户信息
  public readonly userId: string;
  public readonly appVersion?: string; // 应用版本号，由外部传入

  // 隐私协议（动态控制日志可用性）
  public readonly privacyAgreed: boolean;

  // 文件配置（简化）
  public readonly maxFileSize: number;        // 单个文件大小限制
  public readonly maxDirectorySize: number;   // 整个目录大小限制
  public readonly logDirectory?: string;      // 日志存储目录

  // 上传配置
  public readonly uploadUrl?: string;

  constructor(builder: UpLogConfigBuilder) {
    this.logLevel = builder.logLevel;
    this.enableConsoleOutput = builder.enableConsoleOutput;
    this.enableFileOutput = builder.enableFileOutput;
    this.enableFullLog = builder.enableFullLog;
    this.logEnv = builder.logEnv;
    this.disableSensitiveWords = builder.disableSensitiveWords;
    this.userId = builder.userId;
    this.appVersion = builder.appVersion;
    this.privacyAgreed = builder.privacyAgreed;
    this.maxFileSize = builder.maxFileSize;
    this.maxDirectorySize = builder.maxDirectorySize;
    this.logDirectory = builder.logDirectory;
    this.uploadUrl = builder.uploadUrl;
  }

  /**
   * 创建Builder
   */
  static builder(): UpLogConfigBuilder {
    return new UpLogConfigBuilder();
  }

  /**
   * 转换为JSON对象，用于传递给N-API层（已废弃）
   */
  toJSON(): UpLogConfigJSON {
    return {
      logLevel: this.logLevel,
      enableConsoleOutput: this.enableConsoleOutput,
      enableFileOutput: this.enableFileOutput,
      enableFullLog: this.enableFullLog,
      logEnv: this.logEnv,
      disableSensitiveWords: this.disableSensitiveWords,
      userId: this.userId,
      appVersion: this.appVersion || "",
      privacyAgreed: this.privacyAgreed,
      maxFileSize: this.maxFileSize,
      maxDirectorySize: this.maxDirectorySize,
      logDirectory: this.logDirectory || "",
      uploadUrl: this.uploadUrl || ""
    };
  }

  /**
   * 转换为LoggerConfig对象，用于传递给rust_logger
   */
  toLoggerConfig(): LoggerConfig {
    const config = new LoggerConfig();

    // 基础配置
    config.logLevel = this.convertLogLevel(this.logLevel);
    config.enableConsoleOutput = this.enableConsoleOutput;
    config.enableFullLog = this.enableFullLog;
    config.testMode = false; // 默认非测试模式
    config.logEnv = this.logEnv;
    config.disableSensitiveWords = !this.disableSensitiveWords; // 注意：disableSensitiveWords取反

    // 用户信息
    config.userId = this.userId;
    config.deviceId = ""; // 设备ID由外部传入，这里使用默认值
    config.sessionId = ""; // 会话ID由Rust层生成
    config.appVersion = this.appVersion || "1.0.0";

    // 隐私配置
    config.privacyAgreed = this.privacyAgreed;
    config.isDebugMode = false; // 默认非调试模式

    // 文件配置
    config.maxFileSize = this.maxFileSize;
    config.maxDirectorySize = this.maxDirectorySize;
    config.logFilePrefix = "uplog"; // 默认前缀
    config.logDirectory = this.logDirectory || "";

    // 性能配置
    config.maxLogLength = 4000; // 默认最大日志长度
    config.maxLogsPerSecond = 0; // 默认不限流

    // HarmonyOS对齐的关键配置
    config.versionName = this.appVersion || "1.0.0";

    return config;
  }

  /**
   * 转换日志级别
   * UpLog LogLevel -> Rust LogLevel映射
   */
  private convertLogLevel(level: LogLevel): RustLogLevel {
    switch (level) {
      case LogLevel.VERBOSE:
        return RustLogLevel.DEBUG; // VERBOSE映射到DEBUG
      case LogLevel.DEBUG:
        return RustLogLevel.DEBUG;
      case LogLevel.INFO:
        return RustLogLevel.INFO;
      case LogLevel.WARN:
        return RustLogLevel.WARN;
      case LogLevel.ERROR:
        return RustLogLevel.ERROR;
      default:
        return RustLogLevel.WARN;
    }
  }
}

/**
 * UpLogConfig Builder类
 * 使用Builder模式构建配置
 */
export class UpLogConfigBuilder {
  public logLevel: LogLevel = LogLevel.WARN;
  public enableConsoleOutput: boolean = true;
  public enableFileOutput: boolean = true;
  public enableFullLog: boolean = false; // 默认不开启全量日志
  public logEnv: LogEnv = LogEnv.SC; // 默认生产环境
  public disableSensitiveWords: boolean = false; // 默认启用脱敏
  public userId: string = "0"; // 默认值为"0"
  public appVersion?: string; // 应用版本号，由外部传入
  public privacyAgreed: boolean = false;
  public maxFileSize: number = 20 * 1024 * 1024; // 20MB (与Android一致)
  public maxDirectorySize: number = 600 * 1024 * 1024; // 600MB (与Android DEFAULT_TOTAL_LOG_FILE_SIZE一致)
  public logDirectory?: string; // 日志存储目录，如果不设置则使用默认值
  public uploadUrl?: string;

  /**
   * 设置日志级别
   */
  setLogLevel(level: LogLevel): UpLogConfigBuilder {
    this.logLevel = level;
    return this;
  }

  /**
   * 设置控制台输出
   */
  setConsoleOutput(enable: boolean): UpLogConfigBuilder {
    this.enableConsoleOutput = enable;
    return this;
  }

  /**
   * 设置文件输出
   */
  setFileOutput(enable: boolean): UpLogConfigBuilder {
    this.enableFileOutput = enable;
    return this;
  }

  /**
   * 设置全量日志（开启后会触发上传）
   */
  setFullLog(enable: boolean): UpLogConfigBuilder {
    this.enableFullLog = enable;
    return this;
  }

  /**
   * 设置日志环境
   */
  setLogEnv(env: LogEnv): UpLogConfigBuilder {
    this.logEnv = env;
    return this;
  }

  /**
   * 设置日志脱敏开关
   */
  setDisableSensitiveWords(disable: boolean): UpLogConfigBuilder {
    this.disableSensitiveWords = disable;
    return this;
  }

  /**
   * 设置用户ID
   */
  setUserId(userId: string): UpLogConfigBuilder {
    this.userId = userId;
    return this;
  }

  /**
   * 设置应用版本号
   */
  setAppVersion(appVersion: string): UpLogConfigBuilder {
    this.appVersion = appVersion;
    return this;
  }



  /**
   * 设置隐私协议同意状态
   */
  setPrivacyAgreed(agreed: boolean): UpLogConfigBuilder {
    this.privacyAgreed = agreed;
    return this;
  }

  /**
   * 设置文件配置
   */
  setFileConfig(maxFileSize: number, maxDirectorySize: number): UpLogConfigBuilder {
    this.maxFileSize = maxFileSize;
    this.maxDirectorySize = maxDirectorySize;
    return this;
  }

  /**
   * 设置日志存储目录
   */
  setLogDirectory(logDirectory: string): UpLogConfigBuilder {
    this.logDirectory = logDirectory;
    return this;
  }

  /**
   * 设置上传配置
   */
  setUploadUrl(uploadUrl: string): UpLogConfigBuilder {
    this.uploadUrl = uploadUrl;
    return this;
  }

  /**
   * 构建配置对象
   */
  build(): UpLogConfig {
    return new UpLogConfig(this);
  }
}
