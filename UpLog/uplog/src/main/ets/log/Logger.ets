import hilog from '@ohos.hilog';
import { LogLevel } from './LogLevel';
import { isLoggable } from './UpLoggerHelper';
import { RustLogger } from '@uplus/rust_logger';

export class Logger {
  private domain: number;
  private prefix: string;

  constructor(prefix: string) {
    this.prefix = prefix;
    this.domain = 0xFF00;
  }

  debug(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.DEBUG)) {
      // 调用rust_logger桥接层，保持鸿蒙格式
      // 格式转换由桥接层处理
      RustLogger.debug(this.prefix, format, args);
    }
  }

  info(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.INFO)) {
      // 调用rust_logger桥接层，保持鸿蒙格式
      // 格式转换由桥接层处理
      RustLogger.info(this.prefix, format, args);
    }
  }

  warn(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.WARN)) {
      // 调用rust_logger桥接层，保持鸿蒙格式
      // 格式转换由桥接层处理
      RustLogger.warn(this.prefix, format, args);
    }
  }

  error(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.ERROR)) {
      // 调用rust_logger桥接层，保持鸿蒙格式
      // 格式转换由桥接层处理
      RustLogger.error(this.prefix, format, args);
    }
  }
}
