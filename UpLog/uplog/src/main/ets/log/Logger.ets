import hilog from '@ohos.hilog';
import { LogLevel } from './LogLevel';
import { isLoggable } from './UpLoggerHelper';
import { RustLogger } from '@uplus/rust_logger';

export class Logger {
  private domain: number;
  private prefix: string;

  constructor(prefix: string) {
    this.prefix = prefix;
    this.domain = 0xFF00;
  }

  /**
   * 转换鸿蒙格式到Rust格式
   * %{public}s -> {}
   * %{private}s -> {}
   * %{public}d -> {}
   * %{private}d -> {}
   */
  private convertHarmonyToRustFormat(format: string): string {
    // 鸿蒙格式正则：%{public|private}[sdi]
    return format.replace(/%\{(?:public|private)\}[sdi]/g, '{}');
  }

  debug(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.DEBUG)) {
      // 同时输出到控制台和文件
      hilog.debug(this.domain, this.prefix, format, ...args);

      // 转换格式并调用rust_logger
      const rustFormat = this.convertHarmonyToRustFormat(format);
      RustLogger.debug(this.prefix, rustFormat, args);
    }
  }

  info(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.INFO)) {
      // 同时输出到控制台和文件
      hilog.info(this.domain, this.prefix, format, ...args);

      // 转换格式并调用rust_logger
      const rustFormat = this.convertHarmonyToRustFormat(format);
      RustLogger.info(this.prefix, rustFormat, args);
    }
  }

  warn(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.WARN)) {
      // 同时输出到控制台和文件
      hilog.warn(this.domain, this.prefix, format, ...args);

      // 转换格式并调用rust_logger
      const rustFormat = this.convertHarmonyToRustFormat(format);
      RustLogger.warn(this.prefix, rustFormat, args);
    }
  }

  error(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.ERROR)) {
      // 同时输出到控制台和文件
      hilog.error(this.domain, this.prefix, format, ...args);

      // 转换格式并调用rust_logger
      const rustFormat = this.convertHarmonyToRustFormat(format);
      RustLogger.error(this.prefix, rustFormat, args);
    }
  }
}
