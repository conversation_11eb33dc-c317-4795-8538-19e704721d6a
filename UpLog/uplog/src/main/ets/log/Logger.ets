import hilog from '@ohos.hilog';
import { LogLevel } from './LogLevel';
import { isLoggable } from './UpLoggerHelper';
import { RustLogger, LogLevel as RustLogLevel } from '@uplus/rust_logger';

export class Logger {
  private domain: number;
  private prefix: string;

  constructor(prefix: string) {
    this.prefix = prefix;
    this.domain = 0xFF00;
  }

  /**
   * 转换UpLog LogLevel到RustLogger LogLevel
   */
  private convertLogLevel(level: LogLevel): RustLogLevel {
    switch (level) {
      case LogLevel.DEBUG:
        return RustLogLevel.DEBUG;
      case LogLevel.INFO:
        return RustLogLevel.INFO;
      case LogLevel.WARN:
        return RustLogLevel.WARN;
      case LogLevel.ERROR:
        return RustLogLevel.ERROR;
      default:
        return RustLogLevel.WARN;
    }
  }

  /**
   * 转换参数数组为字符串数组
   */
  private convertArgs(args: Object[]): string[] {
    return args.map(arg => String(arg));
  }

  debug(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.DEBUG)) {
      // 调用rust_logger桥接层，保持鸿蒙格式
      // 格式转换由桥接层处理
      RustLogger.writeLog(
        this.convertLogLevel(LogLevel.DEBUG),
        this.prefix,
        format,
        this.convertArgs(args)
      );
    }
  }

  info(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.INFO)) {
      // 调用rust_logger桥接层，保持鸿蒙格式
      // 格式转换由桥接层处理
      RustLogger.writeLog(
        this.convertLogLevel(LogLevel.INFO),
        this.prefix,
        format,
        this.convertArgs(args)
      );
    }
  }

  warn(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.WARN)) {
      // 调用rust_logger桥接层，保持鸿蒙格式
      // 格式转换由桥接层处理
      RustLogger.writeLog(
        this.convertLogLevel(LogLevel.WARN),
        this.prefix,
        format,
        this.convertArgs(args)
      );
    }
  }

  error(format: string, ...args: Object[]): void {
    if (isLoggable(LogLevel.ERROR)) {
      // 调用rust_logger桥接层，保持鸿蒙格式
      // 格式转换由桥接层处理
      RustLogger.writeLog(
        this.convertLogLevel(LogLevel.ERROR),
        this.prefix,
        format,
        this.convertArgs(args)
      );
    }
  }
}
